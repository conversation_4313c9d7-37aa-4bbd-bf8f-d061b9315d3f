<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Local AI Services</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Test Local AI Services</h1>
    <p>This page tests the local AI service implementation in the browser extension.</p>

    <div class="test-section info">
        <h3>Instructions</h3>
        <p>1. Make sure you have installed the extension with the local AI services</p>
        <p>2. Configure at least one AI provider with a valid API key in the extension settings</p>
        <p>3. Click the test buttons below to verify the local AI services are working</p>
    </div>

    <div class="test-section">
        <h3>Test AI Request</h3>
        <button id="testAiRequest">Test AI Request</button>
        <button id="checkExtension">Check Extension Status</button>
        <div id="testResults" class="log"></div>
    </div>

    <div class="test-section">
        <h3>Test Form Fill</h3>
        <form id="testForm">
            <div style="margin: 10px 0;">
                <label for="title">Title:</label>
                <input type="text" id="title" name="title" placeholder="Title will be filled here">
            </div>
            <div style="margin: 10px 0;">
                <label for="description">Description:</label>
                <textarea id="description" name="description" rows="4" placeholder="Description will be filled here"></textarea>
            </div>
            <div style="margin: 10px 0;">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" placeholder="Email will be filled here">
            </div>
        </form>
        <button id="testFormFill">Test Form Fill</button>
        <div id="formResults" class="log"></div>
    </div>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            element.textContent += logMessage;
            element.scrollTop = element.scrollHeight;
            
            // Change background color based on type
            if (type === 'success') {
                element.style.backgroundColor = '#d4edda';
            } else if (type === 'error') {
                element.style.backgroundColor = '#f8d7da';
            } else {
                element.style.backgroundColor = '#f8f9fa';
            }
        }

        function clearLog(elementId) {
            document.getElementById(elementId).textContent = '';
            document.getElementById(elementId).style.backgroundColor = '#f8f9fa';
        }

        // Check if extension is available
        document.getElementById('checkExtension').addEventListener('click', async () => {
            clearLog('testResults');
            log('testResults', 'Checking extension status...');
            
            try {
                if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
                    log('testResults', 'Chrome extension API is available', 'success');
                    
                    // Try to get extension status
                    const response = await chrome.runtime.sendMessage({ type: 'getLoginStatus' });
                    log('testResults', `Extension response: ${JSON.stringify(response)}`, 'success');
                } else {
                    log('testResults', 'Chrome extension API is not available. Make sure the extension is installed and this page is loaded in a browser with the extension.', 'error');
                }
            } catch (error) {
                log('testResults', `Error checking extension: ${error.message}`, 'error');
            }
        });

        // Test AI request
        document.getElementById('testAiRequest').addEventListener('click', async () => {
            clearLog('testResults');
            log('testResults', 'Testing AI request...');
            
            try {
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    throw new Error('Chrome extension API not available');
                }

                const testRequest = {
                    type: 'aiRequest',
                    prompt: 'Generate a simple test response',
                    options: {
                        mode: 'general',
                        language: 'en',
                        description: 'This is a test request to verify local AI services are working',
                        formFields: [
                            { name: 'title', type: 'text' },
                            { name: 'description', type: 'textarea' }
                        ]
                    }
                };

                log('testResults', `Sending request: ${JSON.stringify(testRequest, null, 2)}`);
                
                const response = await chrome.runtime.sendMessage(testRequest);
                
                log('testResults', `Response received: ${JSON.stringify(response, null, 2)}`);
                
                if (response.success) {
                    log('testResults', 'AI request successful! Local AI services are working.', 'success');
                } else {
                    log('testResults', `AI request failed: ${response.error}`, 'error');
                }
            } catch (error) {
                log('testResults', `Error testing AI request: ${error.message}`, 'error');
            }
        });

        // Test form fill
        document.getElementById('testFormFill').addEventListener('click', async () => {
            clearLog('formResults');
            log('formResults', 'Testing form fill...');
            
            try {
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    throw new Error('Chrome extension API not available');
                }

                // First, get AI generated content
                const aiRequest = {
                    type: 'aiRequest',
                    prompt: 'Generate content for a bug report form',
                    options: {
                        mode: 'bug_report',
                        language: 'en',
                        description: 'Generate a sample bug report about a login issue',
                        formFields: [
                            { name: 'title', type: 'text' },
                            { name: 'description', type: 'textarea' },
                            { name: 'email', type: 'email' }
                        ]
                    }
                };

                log('formResults', 'Requesting AI content...');
                const aiResponse = await chrome.runtime.sendMessage(aiRequest);
                
                if (aiResponse.success) {
                    log('formResults', `AI content generated: ${JSON.stringify(aiResponse.data, null, 2)}`);
                    
                    // Fill the form with the generated content
                    const data = aiResponse.data;
                    if (data.title) {
                        document.getElementById('title').value = data.title;
                        log('formResults', `Filled title: ${data.title}`);
                    }
                    if (data.description) {
                        document.getElementById('description').value = data.description;
                        log('formResults', `Filled description: ${data.description}`);
                    }
                    if (data.email) {
                        document.getElementById('email').value = data.email;
                        log('formResults', `Filled email: ${data.email}`);
                    }
                    
                    log('formResults', 'Form fill test completed successfully!', 'success');
                } else {
                    log('formResults', `AI request failed: ${aiResponse.error}`, 'error');
                }
            } catch (error) {
                log('formResults', `Error testing form fill: ${error.message}`, 'error');
            }
        });
    </script>
</body>
</html>
